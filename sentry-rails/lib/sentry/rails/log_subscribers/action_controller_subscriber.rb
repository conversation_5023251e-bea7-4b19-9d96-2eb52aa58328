# frozen_string_literal: true

require "sentry/rails/log_subscriber"

module Sentry
  module Rails
    module LogSubscribers
      # LogSubscriber for ActionController events that captures HTTP request processing
      # and logs them using Sentry's structured logging system.
      #
      # This subscriber captures process_action.action_controller events and formats them
      # with relevant request information including controller, action, HTTP status,
      # request parameters, and performance metrics.
      #
      # @example Usage
      #   # Enable structured logging for ActionController
      #   Sentry.init do |config|
      #     config.enable_logs = true
      #     config.rails.structured_logging = true
      #     config.rails.structured_logging.attach_to = [:action_controller]
      #   end
      class ActionControllerSubscriber < Sentry::Rails::LogSubscriber
        # Handle process_action.action_controller events
        #
        # @param event [ActiveSupport::Notifications::Event] The controller action event
        def process_action(event)
          return if excluded_event?(event)

          payload = event.payload
          controller = payload[:controller]
          action = payload[:action]
          status = extract_status_from_payload(payload)
          duration = duration_ms(event)

          attributes = {
            controller: controller,
            action: action,
            status: status,
            duration_ms: duration,
            method: payload[:method],
            path: payload[:path],
            format: payload[:format]
          }

          attributes[:view_runtime_ms] = payload[:view_runtime]&.round(2) if payload[:view_runtime]
          attributes[:db_runtime_ms] = payload[:db_runtime]&.round(2) if payload[:db_runtime]

          if Sentry.configuration.send_default_pii && payload[:params]
            filtered_params = filter_sensitive_params(payload[:params])
            attributes[:params] = filtered_params unless filtered_params.empty?
          end

          level = level_for_request(status, duration)
          message = "#{controller}##{action}"

          log_structured_event(
            message: message,
            level: level,
            attributes: attributes
          )
        end

        private

        def level_for_request(status, duration_ms)
          return handle_nil_status if status.nil?

          return :error if status >= 500
          return :warn if status >= 400

          :info
        end

        def filter_sensitive_params(params)
          return {} unless params.is_a?(Hash)

          sensitive_keys = %w[
            password password_confirmation
            secret token api_key
            credit_card ssn social_security_number
            authorization auth
          ]

          params.reject do |key, _value|
            key_str = key.to_s.downcase
            sensitive_keys.any? { |sensitive| key_str.include?(sensitive) }
          end
        end

        if ::Rails.version.to_f >= 6.1
          def extract_status_from_payload(payload)
            status = payload[:status]

            if status.nil? && payload[:exception]
              status = infer_status_from_exception(payload[:exception])
            end

            status
          end

          def handle_nil_status
            :error
          end

          private

          def infer_status_from_exception(exception_info)
            exception_class = exception_info.first if exception_info.is_a?(Array)

            case exception_class
            when "ActionController::BadRequest"
              400
            when "ActionController::Forbidden"
              403
            when "ActionController::NotFound", "ActiveRecord::RecordNotFound"
              404
            when "ActionController::MethodNotAllowed"
              405
            when "ActionController::NotAcceptable"
              406
            when "ActionController::RequestTimeout"
              408
            when "ActionController::UnprocessableEntity"
              422
            else
              500
            end
          end
        else
          def extract_status_from_payload(payload)
            payload[:status]
          end

          def handle_nil_status
            :warn
          end
        end
      end
    end
  end
end
